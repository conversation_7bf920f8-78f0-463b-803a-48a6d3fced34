<!-- URL Shortener -->
<div class="space-y-6" x-data="{ 
    originalUrl: '', 
    customAlias: '', 
    shortUrl: '', 
    isLoading: false,
    showResult: false,
    copied: false,
    async shortenUrl() {
        if (!this.originalUrl) return;
        
        this.isLoading = true;
        this.showResult = false;
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Generate random short code
        const randomCode = Math.random().toString(36).substring(2, 8);
        this.shortUrl = `https://s4s.plus/${this.customAlias || randomCode}`;
        
        this.isLoading = false;
        this.showResult = true;
    },
    copyToClipboard() {
        navigator.clipboard.writeText(this.shortUrl);
        this.copied = true;
        setTimeout(() => this.copied = false, 2000);
    }
}">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h2 class="text-2xl font-bold text-gray-900">URL Shortener</h2>
                <p class="text-gray-600">Create short links and start earning money from clicks</p>
            </div>
        </div>
    </div>

    <!-- Shortener Form -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <form @submit.prevent="shortenUrl()" class="space-y-6">
            <!-- Original URL Input -->
            <div>
                <label for="original-url" class="block text-sm font-medium text-gray-700 mb-2">
                    Original URL <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                        </svg>
                    </div>
                    <input id="original-url" 
                           x-model="originalUrl"
                           type="url" 
                           required 
                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="https://example.com/your-long-url">
                </div>
                <p class="mt-2 text-xs text-gray-500">Enter the URL you want to shorten</p>
            </div>

            <!-- Custom Alias (Optional) -->
            <div>
                <label for="custom-alias" class="block text-sm font-medium text-gray-700 mb-2">
                    Custom Alias <span class="text-gray-400 text-xs">(Optional)</span>
                </label>
                <div class="flex">
                    <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                        s4s.plus/
                    </span>
                    <input id="custom-alias" 
                           x-model="customAlias"
                           type="text" 
                           class="flex-1 block w-full px-3 py-3 border border-gray-300 rounded-r-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="my-custom-link">
                </div>
                <p class="mt-2 text-xs text-gray-500">Leave empty for random alias</p>
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" 
                        :disabled="!originalUrl || isLoading"
                        class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    <span x-show="!isLoading">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                        </svg>
                        Shorten URL
                    </span>
                    <span x-show="isLoading" x-cloak class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating Short Link...
                    </span>
                </button>
            </div>
        </form>
    </div>

    <!-- Result -->
    <div x-show="showResult" x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Short Link Created!</h3>
            <p class="text-sm text-gray-600 mb-6">Your link has been shortened successfully</p>
            
            <!-- Short URL Display -->
            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-lg font-medium text-blue-600 truncate" x-text="shortUrl"></p>
                    </div>
                    <button @click="copyToClipboard()" 
                            class="ml-4 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg x-show="!copied" class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                        <svg x-show="copied" x-cloak class="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span x-text="copied ? 'Copied!' : 'Copy'"></span>
                    </button>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button @click="showResult = false; originalUrl = ''; customAlias = ''; shortUrl = ''" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Create Another Link
                </button>
                <button @click="currentPage = 'analytics'" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    View Analytics
                </button>
            </div>
        </div>
    </div>

    <!-- Features Info -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Bronze Plan Features</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">Basic Analytics</p>
                    <p class="text-xs text-gray-500">Track clicks and basic stats</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">Unlimited Links</p>
                    <p class="text-xs text-gray-500">Create as many links as you want</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">Monetization</p>
                    <p class="text-xs text-gray-500">Earn money from your links</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-amber-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">Custom Domain</p>
                    <p class="text-xs text-gray-500">Upgrade to Premium for custom domains</p>
                </div>
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                </svg>
                <div>
                    <p class="text-sm font-medium text-blue-800">Want more features?</p>
                    <p class="text-xs text-blue-600">Upgrade to Premium or Master plan for advanced analytics, custom domains, and higher earnings!</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH B:\laragon\www\sub4short\resources\views/bronze-plan/pages/shortener.blade.php ENDPATH**/ ?>