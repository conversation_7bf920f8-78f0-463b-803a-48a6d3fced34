<!-- Settings Page -->
<div class="space-y-6" x-data="{ 
    activeTab: 'profile',
    profileData: {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '***********',
        bio: 'Digital marketer and content creator'
    },
    passwordData: {
        current: '',
        new: '',
        confirm: ''
    },
    notificationSettings: {
        emailNotifications: true,
        pushNotifications: false,
        weeklyReports: true,
        monthlyReports: true
    },
    paymentSettings: {
        defaultMethod: 'bank',
        bankName: 'BCA',
        accountNumber: '**********',
        accountName: '<PERSON>'
    },
    isLoading: false,
    showSuccess: false,
    async saveSettings() {
        this.isLoading = true;
        await new Promise(resolve => setTimeout(resolve, 1500));
        this.isLoading = false;
        this.showSuccess = true;
        setTimeout(() => this.showSuccess = false, 3000);
    }
}">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h2 class="text-2xl font-bold text-gray-900">Settings</h2>
                <p class="text-gray-600">Manage your account settings and preferences</p>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div x-show="showSuccess" x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         class="bg-green-50 border border-green-200 rounded-xl p-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            <p class="text-green-800 font-medium">Settings saved successfully!</p>
        </div>
    </div>

    <!-- Settings Navigation -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button @click="activeTab = 'profile'" 
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'profile' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                    Profile
                </button>
                <button @click="activeTab = 'security'" 
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'security' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                    Security
                </button>
                <button @click="activeTab = 'notifications'" 
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'notifications' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                    Notifications
                </button>
                <button @click="activeTab = 'payment'" 
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'payment' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                    Payment
                </button>
            </nav>
        </div>

        <!-- Profile Settings -->
        <div x-show="activeTab === 'profile'" x-cloak class="p-6">
            <form @submit.prevent="saveSettings()" class="space-y-6">
                <div class="flex items-center space-x-6">
                    <div class="flex-shrink-0">
                        <img class="h-20 w-20 rounded-full object-cover" 
                             src="https://ui-avatars.com/api/?name=John+Doe&background=3b82f6&color=fff&size=80" 
                             alt="Profile">
                    </div>
                    <div>
                        <button type="button" class="bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Change Photo
                        </button>
                        <p class="text-xs text-gray-500 mt-2">JPG, GIF or PNG. 1MB max.</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input id="name" x-model="profileData.name" type="text" required 
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input id="email" x-model="profileData.email" type="email" required 
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input id="phone" x-model="profileData.phone" type="tel" 
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="plan" class="block text-sm font-medium text-gray-700 mb-2">Current Plan</label>
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-amber-100 text-amber-800">
                                Bronze Creator
                            </span>
                            <button type="button" class="ml-3 text-sm text-blue-600 hover:text-blue-500 font-medium">
                                Upgrade Plan
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                    <textarea id="bio" x-model="profileData.bio" rows="3" 
                              class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Tell us about yourself..."></textarea>
                </div>

                <div class="flex justify-end">
                    <button type="submit" :disabled="isLoading"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        <span x-show="!isLoading">Save Changes</span>
                        <span x-show="isLoading" x-cloak class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Security Settings -->
        <div x-show="activeTab === 'security'" x-cloak class="p-6">
            <form @submit.prevent="saveSettings()" class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                    <div class="space-y-4">
                        <div>
                            <label for="current-password" class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                            <input id="current-password" x-model="passwordData.current" type="password" required 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                            <input id="new-password" x-model="passwordData.new" type="password" required 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                            <input id="confirm-password" x-model="passwordData.confirm" type="password" required 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Two-Factor Authentication</h3>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-gray-900">SMS Authentication</p>
                            <p class="text-xs text-gray-500">Receive verification codes via SMS</p>
                        </div>
                        <button type="button" class="bg-white border border-gray-300 rounded-md py-2 px-3 text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Enable
                        </button>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" :disabled="isLoading"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        Update Security Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Notification Settings -->
        <div x-show="activeTab === 'notifications'" x-cloak class="p-6">
            <form @submit.prevent="saveSettings()" class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">General Notifications</p>
                                <p class="text-xs text-gray-500">Receive updates about your account and links</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" x-model="notificationSettings.emailNotifications" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Weekly Reports</p>
                                <p class="text-xs text-gray-500">Get weekly performance summaries</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" x-model="notificationSettings.weeklyReports" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Monthly Reports</p>
                                <p class="text-xs text-gray-500">Get monthly performance summaries</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" x-model="notificationSettings.monthlyReports" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Push Notifications</h3>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Browser Notifications</p>
                            <p class="text-xs text-gray-500">Receive real-time notifications in your browser</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" x-model="notificationSettings.pushNotifications" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" :disabled="isLoading"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        Save Notification Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Payment Settings -->
        <div x-show="activeTab === 'payment'" x-cloak class="p-6">
            <form @submit.prevent="saveSettings()" class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Default Payment Method</h3>
                    <div class="space-y-4">
                        <div>
                            <label for="payment-method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                            <select id="payment-method" x-model="paymentSettings.defaultMethod" 
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="bank">Bank Transfer</option>
                                <option value="ewallet">E-Wallet</option>
                            </select>
                        </div>
                        
                        <div x-show="paymentSettings.defaultMethod === 'bank'" class="space-y-4">
                            <div>
                                <label for="bank-name-setting" class="block text-sm font-medium text-gray-700 mb-2">Bank Name</label>
                                <select id="bank-name-setting" x-model="paymentSettings.bankName" 
                                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="bca">BCA</option>
                                    <option value="mandiri">Bank Mandiri</option>
                                    <option value="bni">BNI</option>
                                    <option value="bri">BRI</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="account-number-setting" class="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
                                <input id="account-number-setting" x-model="paymentSettings.accountNumber" type="text" 
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label for="account-name-setting" class="block text-sm font-medium text-gray-700 mb-2">Account Holder Name</label>
                                <input id="account-name-setting" x-model="paymentSettings.accountName" type="text" 
                                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" :disabled="isLoading"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        Save Payment Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH B:\laragon\www\sub4short\resources\views/bronze-plan/pages/settings.blade.php ENDPATH**/ ?>