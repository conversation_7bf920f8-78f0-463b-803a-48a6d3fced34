<!-- Upgrade Plan Page -->
<div class="space-y-6" x-data="{ 
    showPaymentModal: false,
    selectedPlan: null,
    paymentMethod: 'bank',
    bankName: '',
    ewalletType: 'gopay',
    isProcessing: false,
    showSuccess: false,
    plans: {
        platinum: {
            name: 'Platinum',
            price: 50000,
            duration: 'month',
            features: [
                'Advanced Analytics',
                'Custom Domains',
                'Priority Support',
                'Higher Earnings Rate',
                'Unlimited Links',
                'API Access',
                'Custom Branding',
                'Bulk Link Creation'
            ],
            color: 'blue'
        },
        master: {
            name: 'Master',
            price: 150000,
            duration: 'month',
            features: [
                'All Platinum Features',
                'White Label Solution',
                'Advanced API',
                'Premium Support',
                'Maximum Earnings Rate',
                'Team Management',
                'Advanced Security',
                'Custom Integrations',
                'Dedicated Account Manager'
            ],
            color: 'purple'
        }
    },
    selectPlan(planKey) {
        this.selectedPlan = planKey;
        this.showPaymentModal = true;
    },
    async processPayment() {
        this.isProcessing = true;
        
        // Simulate payment processing
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        this.isProcessing = false;
        this.showPaymentModal = false;
        this.showSuccess = true;
        
        // Hide success message after 5 seconds
        setTimeout(() => this.showSuccess = false, 5000);
    }
}">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h2 class="text-2xl font-bold text-gray-900">Upgrade Your Plan</h2>
                <p class="text-gray-600">Unlock more features and higher earnings with our premium plans</p>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div x-show="showSuccess" x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         class="bg-green-50 border border-green-200 rounded-xl p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-green-800">Payment Successful!</h3>
                <p class="text-green-700 mt-1">Your plan has been upgraded successfully. You can now enjoy all the premium features!</p>
            </div>
        </div>
    </div>

    <!-- Current Plan -->
    <div class="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-xl p-6 border border-amber-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-amber-800">Current Plan: Bronze Creator</h3>
                    <p class="text-amber-700">Free plan with basic features</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                    Free
                </span>
            </div>
        </div>
        
        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-amber-800">24</p>
                <p class="text-xs text-amber-600">Total Links</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-amber-800">1,247</p>
                <p class="text-xs text-amber-600">Total Clicks</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-amber-800">Rp 45K</p>
                <p class="text-xs text-amber-600">Earnings</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-amber-800">Basic</p>
                <p class="text-xs text-amber-600">Analytics</p>
            </div>
        </div>
    </div>

    <!-- Pricing Plans -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Platinum Plan -->
        <div class="bg-white rounded-xl shadow-sm border-2 border-blue-200 relative overflow-hidden">
            <div class="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-xs font-medium rounded-bl-lg">
                Popular
            </div>
            <div class="p-6">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900">Platinum</h3>
                    <div class="mt-2">
                        <span class="text-4xl font-bold text-blue-600">Rp 50,000</span>
                        <span class="text-gray-500">/month</span>
                    </div>
                    <p class="text-gray-600 mt-2">Perfect for content creators and marketers</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Advanced Analytics</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Custom Domains</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Priority Support</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Higher Earnings Rate</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Unlimited Links</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">API Access</span>
                    </li>
                </ul>
                
                <button @click="selectPlan('platinum')" 
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Upgrade to Platinum
                </button>
            </div>
        </div>

        <!-- Master Plan -->
        <div class="bg-white rounded-xl shadow-sm border-2 border-purple-200 relative overflow-hidden">
            <div class="absolute top-0 right-0 bg-purple-500 text-white px-3 py-1 text-xs font-medium rounded-bl-lg">
                Premium
            </div>
            <div class="p-6">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900">Master</h3>
                    <div class="mt-2">
                        <span class="text-4xl font-bold text-purple-600">Rp 150,000</span>
                        <span class="text-gray-500">/month</span>
                    </div>
                    <p class="text-gray-600 mt-2">For businesses and power users</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">All Platinum Features</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">White Label Solution</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Advanced API</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Premium Support</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Maximum Earnings Rate</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="text-gray-700">Team Management</span>
                    </li>
                </ul>
                
                <button @click="selectPlan('master')" 
                        class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors">
                    Upgrade to Master
                </button>
            </div>
        </div>
    </div>

    <!-- Feature Comparison -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Feature Comparison</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Features</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Bronze</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Platinum</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Master</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Links per Month</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">1,000</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Unlimited</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Analytics</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Basic</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Advanced</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Advanced+</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Custom Domain</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">API Access</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Support</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Basic</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Priority</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Premium</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Payment Modal -->
    <div x-show="showPaymentModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <!-- Modal Header -->
                <div class="bg-white px-6 pt-6 pb-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                                     :class="selectedPlan === 'platinum' ? 'bg-blue-100' : 'bg-purple-100'">
                                    <svg class="w-6 h-6" :class="selectedPlan === 'platinum' ? 'text-blue-600' : 'text-purple-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    Upgrade to <span x-text="selectedPlan ? plans[selectedPlan].name : ''"></span>
                                </h3>
                                <p class="text-sm text-gray-600">Complete your payment to upgrade your plan</p>
                            </div>
                        </div>
                        <button @click="showPaymentModal = false" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="px-6 pb-6">
                    <!-- Plan Summary -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-gray-900" x-text="selectedPlan ? plans[selectedPlan].name + ' Plan' : ''"></h4>
                                <p class="text-sm text-gray-600">Monthly subscription</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-900" x-text="selectedPlan ? 'Rp ' + plans[selectedPlan].price.toLocaleString('id-ID') : ''"></p>
                                <p class="text-sm text-gray-600">/month</p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Selection -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Payment Method</h4>
                        <div class="space-y-3">
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="paymentMethod === 'bank' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'">
                                <input type="radio" x-model="paymentMethod" value="bank" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3"
                                         :class="paymentMethod === 'bank' ? 'border-blue-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-blue-500" x-show="paymentMethod === 'bank'"></div>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                        </svg>
                                        <span class="font-medium text-gray-900">Bank Transfer</span>
                                    </div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="paymentMethod === 'ewallet' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'">
                                <input type="radio" x-model="paymentMethod" value="ewallet" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3"
                                         :class="paymentMethod === 'ewallet' ? 'border-blue-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-blue-500" x-show="paymentMethod === 'ewallet'"></div>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                        </svg>
                                        <span class="font-medium text-gray-900">E-Wallet</span>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Bank Transfer Details -->
                    <div x-show="paymentMethod === 'bank'" x-cloak class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Select Bank</h4>
                        <select x-model="bankName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Choose Bank</option>
                            <option value="bca">BCA</option>
                            <option value="mandiri">Bank Mandiri</option>
                            <option value="bni">BNI</option>
                            <option value="bri">BRI</option>
                            <option value="cimb">CIMB Niaga</option>
                        </select>

                        <div x-show="bankName" x-cloak class="mt-4 p-4 bg-blue-50 rounded-lg">
                            <h5 class="font-medium text-blue-900 mb-2">Transfer Details</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Bank:</span>
                                    <span class="font-medium text-blue-900" x-text="bankName.toUpperCase()"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Account Number:</span>
                                    <span class="font-medium text-blue-900">**********</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Account Name:</span>
                                    <span class="font-medium text-blue-900">Sub4Short Plus</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Amount:</span>
                                    <span class="font-medium text-blue-900" x-text="selectedPlan ? 'Rp ' + plans[selectedPlan].price.toLocaleString('id-ID') : ''"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- E-Wallet Details -->
                    <div x-show="paymentMethod === 'ewallet'" x-cloak class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Select E-Wallet</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="ewalletType === 'gopay' ? 'border-green-500 bg-green-50' : 'border-gray-300'">
                                <input type="radio" x-model="ewalletType" value="gopay" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2"
                                         :class="ewalletType === 'gopay' ? 'border-green-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-green-500" x-show="ewalletType === 'gopay'"></div>
                                    </div>
                                    <span class="text-sm font-medium">GoPay</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="ewalletType === 'ovo' ? 'border-purple-500 bg-purple-50' : 'border-gray-300'">
                                <input type="radio" x-model="ewalletType" value="ovo" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2"
                                         :class="ewalletType === 'ovo' ? 'border-purple-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-purple-500" x-show="ewalletType === 'ovo'"></div>
                                    </div>
                                    <span class="text-sm font-medium">OVO</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="ewalletType === 'dana' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'">
                                <input type="radio" x-model="ewalletType" value="dana" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2"
                                         :class="ewalletType === 'dana' ? 'border-blue-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-blue-500" x-show="ewalletType === 'dana'"></div>
                                    </div>
                                    <span class="text-sm font-medium">DANA</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                   :class="ewalletType === 'shopeepay' ? 'border-orange-500 bg-orange-50' : 'border-gray-300'">
                                <input type="radio" x-model="ewalletType" value="shopeepay" class="sr-only">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2"
                                         :class="ewalletType === 'shopeepay' ? 'border-orange-500' : 'border-gray-300'">
                                        <div class="w-2 h-2 rounded-full bg-orange-500" x-show="ewalletType === 'shopeepay'"></div>
                                    </div>
                                    <span class="text-sm font-medium">ShopeePay</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-3">
                        <button @click="showPaymentModal = false"
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                        <button @click="processPayment()"
                                :disabled="isProcessing || (paymentMethod === 'bank' && !bankName)"
                                class="flex-1 px-4 py-2 rounded-lg text-white font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                                :class="selectedPlan === 'platinum' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-purple-600 hover:bg-purple-700'">
                            <span x-show="!isProcessing">
                                <span x-text="selectedPlan ? 'Pay Rp ' + plans[selectedPlan].price.toLocaleString('id-ID') : ''"></span>
                            </span>
                            <span x-show="isProcessing" x-cloak class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
