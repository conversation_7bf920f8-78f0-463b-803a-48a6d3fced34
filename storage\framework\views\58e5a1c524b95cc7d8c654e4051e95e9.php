<!-- Withdraw Page -->
<div class="space-y-6" x-data="{ 
    selectedMethod: 'bank',
    amount: '',
    bankAccount: '',
    bankName: '',
    accountName: '',
    ewalletNumber: '',
    ewalletType: 'gopay',
    isProcessing: false,
    showSuccess: false,
    minWithdraw: 50000,
    currentBalance: 45000,
    async processWithdraw() {
        this.isProcessing = true;
        
        // Simulate API call
        await new <PERSON>(resolve => setTimeout(resolve, 2000));
        
        this.isProcessing = false;
        this.showSuccess = true;
    }
}">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h2 class="text-2xl font-bold text-gray-900">Withdraw Earnings</h2>
                <p class="text-gray-600">Cash out your earnings to your preferred payment method</p>
            </div>
        </div>
    </div>

    <!-- Balance Overview -->
    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-green-100 text-sm">Available Balance</p>
                <p class="text-3xl font-bold" x-text="'Rp ' + currentBalance.toLocaleString('id-ID')"></p>
                <p class="text-green-100 text-sm mt-1">From 1,247 total clicks</p>
            </div>
            <div class="hidden md:block">
                <svg class="h-16 w-16 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Minimum Withdraw Notice -->
    <div class="bg-amber-50 border border-amber-200 rounded-xl p-4" x-show="currentBalance < minWithdraw">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-amber-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
            </svg>
            <div>
                <p class="text-amber-800 font-medium">Minimum Withdrawal Not Met</p>
                <p class="text-amber-700 text-sm">You need at least Rp 50,000 to withdraw. Keep earning to reach the minimum!</p>
            </div>
        </div>
    </div>

    <!-- Withdraw Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200" x-show="currentBalance >= minWithdraw">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Withdrawal Request</h3>
        </div>
        
        <form @submit.prevent="processWithdraw()" class="p-6 space-y-6">
            <!-- Amount -->
            <div>
                <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                    Withdrawal Amount <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 text-sm">Rp</span>
                    </div>
                    <input id="amount" 
                           x-model="amount"
                           type="number" 
                           :min="minWithdraw"
                           :max="currentBalance"
                           required 
                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="50000">
                </div>
                <p class="mt-2 text-xs text-gray-500">
                    Minimum: Rp 50,000 | Maximum: <span x-text="'Rp ' + currentBalance.toLocaleString('id-ID')"></span>
                </p>
            </div>

            <!-- Payment Method -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Payment Method <span class="text-red-500">*</span>
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <input id="bank" x-model="selectedMethod" type="radio" value="bank" class="sr-only">
                        <label for="bank" 
                               class="flex items-center p-4 border rounded-lg cursor-pointer transition-colors"
                               :class="selectedMethod === 'bank' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:bg-gray-50'">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                <div>
                                    <p class="font-medium text-gray-900">Bank Transfer</p>
                                    <p class="text-sm text-gray-500">Transfer to your bank account</p>
                                </div>
                            </div>
                        </label>
                    </div>
                    
                    <div class="relative">
                        <input id="ewallet" x-model="selectedMethod" type="radio" value="ewallet" class="sr-only">
                        <label for="ewallet" 
                               class="flex items-center p-4 border rounded-lg cursor-pointer transition-colors"
                               :class="selectedMethod === 'ewallet' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:bg-gray-50'">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                </svg>
                                <div>
                                    <p class="font-medium text-gray-900">E-Wallet</p>
                                    <p class="text-sm text-gray-500">GoPay, OVO, DANA, etc.</p>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Bank Details -->
            <div x-show="selectedMethod === 'bank'" x-cloak class="space-y-4">
                <div>
                    <label for="bank-name" class="block text-sm font-medium text-gray-700 mb-2">
                        Bank Name <span class="text-red-500">*</span>
                    </label>
                    <select id="bank-name" x-model="bankName" required class="block w-full px-3 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Select Bank</option>
                        <option value="bca">BCA</option>
                        <option value="mandiri">Bank Mandiri</option>
                        <option value="bni">BNI</option>
                        <option value="bri">BRI</option>
                        <option value="cimb">CIMB Niaga</option>
                        <option value="danamon">Bank Danamon</option>
                    </select>
                </div>
                
                <div>
                    <label for="account-number" class="block text-sm font-medium text-gray-700 mb-2">
                        Account Number <span class="text-red-500">*</span>
                    </label>
                    <input id="account-number" 
                           x-model="bankAccount"
                           type="text" 
                           required 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="**********">
                </div>
                
                <div>
                    <label for="account-name" class="block text-sm font-medium text-gray-700 mb-2">
                        Account Holder Name <span class="text-red-500">*</span>
                    </label>
                    <input id="account-name" 
                           x-model="accountName"
                           type="text" 
                           required 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="John Doe">
                </div>
            </div>

            <!-- E-Wallet Details -->
            <div x-show="selectedMethod === 'ewallet'" x-cloak class="space-y-4">
                <div>
                    <label for="ewallet-type" class="block text-sm font-medium text-gray-700 mb-2">
                        E-Wallet Type <span class="text-red-500">*</span>
                    </label>
                    <select id="ewallet-type" x-model="ewalletType" required class="block w-full px-3 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="gopay">GoPay</option>
                        <option value="ovo">OVO</option>
                        <option value="dana">DANA</option>
                        <option value="linkaja">LinkAja</option>
                        <option value="shopeepay">ShopeePay</option>
                    </select>
                </div>
                
                <div>
                    <label for="ewallet-number" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    <input id="ewallet-number" 
                           x-model="ewalletNumber"
                           type="tel" 
                           required 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="08123456789">
                </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button type="submit" 
                        :disabled="!amount || amount < minWithdraw || amount > currentBalance || isProcessing"
                        class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all">
                    <span x-show="!isProcessing">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        Submit Withdrawal Request
                    </span>
                    <span x-show="isProcessing" x-cloak class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing Request...
                    </span>
                </button>
            </div>
        </form>
    </div>

    <!-- Success Message -->
    <div x-show="showSuccess" x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Withdrawal Request Submitted!</h3>
            <p class="text-sm text-gray-600 mb-4">Your withdrawal request has been submitted successfully. We'll process it within 1-3 business days.</p>
            <button @click="showSuccess = false; amount = ''; bankAccount = ''; accountName = ''; ewalletNumber = ''" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Make Another Request
            </button>
        </div>
    </div>

    <!-- Withdrawal History -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Withdrawal History</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Rp 100,000</p>
                            <p class="text-xs text-gray-500">Bank Transfer - BCA</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Completed
                        </span>
                        <p class="text-xs text-gray-500 mt-1">Dec 15, 2024</p>
                    </div>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Rp 75,000</p>
                            <p class="text-xs text-gray-500">GoPay</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Processing
                        </span>
                        <p class="text-xs text-gray-500 mt-1">Dec 20, 2024</p>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-500">No more withdrawal history</p>
            </div>
        </div>
    </div>
</div>
<?php /**PATH B:\laragon\www\sub4short\resources\views/bronze-plan/pages/withdraw.blade.php ENDPATH**/ ?>